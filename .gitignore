# Node/Bun dependencies
node_modules/
.bun/

# Bun lock binaries (text lock is ok)
bun.lockb

# Build outputs
/dist/
/build/

# Vite caches
.vite/
.vite-temp/

# TypeScript
*.tsbuildinfo

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
*.log
logs/

# OS files
.DS_Store
Thumbs.db

# IDE/editor
.idea/
.vscode/

# Env files
.env
.env.*.local

# Coverage
coverage/

# Misc
*.local
*.cache
