import Phaser from 'phaser';
import { PlayScene } from './scenes/PlayScene';
import { SelectBirdScene } from './scenes/SelectBirdScene';

const config: Phaser.Types.Core.GameConfig = {
  type: Phaser.AUTO,
  parent: 'app',
  backgroundColor: '#0c1323',
  scale: {
    mode: Phaser.Scale.RESIZE,          // fill parent, no letterboxing
    autoCenter: Phaser.Scale.CENTER_BOTH
  },
  physics: {
    default: 'arcade',
    arcade: {
      gravity: { y: 1200 },
      debug: false
    }
  },
  scene: [SelectBirdScene, PlayScene]
};

// eslint-disable-next-line no-new
new Phaser.Game(config);
